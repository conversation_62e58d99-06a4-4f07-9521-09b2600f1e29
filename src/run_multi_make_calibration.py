import argparse
import glob
import logging
import os
import sys
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from multi_task_core_pool import create_tasks

import portalocker



logger = logging.getLogger()
logger.setLevel(logging.INFO)
# logger.setLevel(logging.WARNING)

# 控制台Handler（INFO级别）
console_handler = logging.StreamHandler()
# console_handler.setLevel(logging.INFO)
console_handler.setLevel(logging.WARNING)

os.makedirs('log_make_calibration', exist_ok=True)
# 文件Handler（DEBUG级别）
file_handler = RotatingFileHandler(
    f'log_make_calibration/multi_make_calibration_{datetime.now().strftime("%Y%m%d_%H%M")}.log',
    maxBytes=10*1024*1024,
    backupCount=30,
    encoding='utf-8'
)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

logger.addHandler(console_handler)
logger.addHandler(file_handler)

files_for_proc = []

now_time = datetime.now()
yesterday_date = now_time - timedelta(hours=12)
default_subdir = yesterday_date.strftime('%Y%m%d')

parser = argparse.ArgumentParser(description='root_dir sub_dir')

parser.add_argument('root_dir', type=str, default='/data/', help='root dir like /home/<USER>/kats_test_data/data/')

parser.add_argument('--sub_dir', type=str, default=default_subdir, help='sub dir like 20250335')

args = parser.parse_args()
full_path = os.path.join(args.root_dir,'??', args.sub_dir)
logger.warning(f" {full_path}")


lock_file = os.path.expanduser('~/multi_core_pool.lock')
lock = portalocker.Lock(lock_file)
try:
    with lock:
        tel_name_list = ['gy1', 'gy2', 'gy3', 'gy4', 'gy5', 'gy6']
        demo_commands = {"commands": []}
        for tel_name_item in tel_name_list:
            command = f"python make_update_calibration.py '{args.root_dir}'  {tel_name_item} --sub_dir {args.sub_dir}"
            cmd_timeout = [None]
            demo_commands["commands"].append({"cmd": [command],  "timeout": cmd_timeout})

        create_tasks(demo_commands, 20)
except portalocker.exceptions.AlreadyLocked:
    logging.error(f"Another instance of the script is already running.{lock_file}  {args.root_dir} {args.sub_dir}")
    sys.exit(1)

