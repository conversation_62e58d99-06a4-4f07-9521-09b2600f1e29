import argparse
import logging
import os
import traceback

import astropy
import ccdproc
import numpy as np
import reproject
from astropy.io import fits
from astropy.nddata import Cutout2D
from astropy.utils.exceptions import AstropyWarning
import warnings
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from pathlib import Path

from astropy.wcs import WCS
from dateutil.relativedelta import relativedelta
from reproject.mosaicking import find_optimal_celestial_wcs, reproject_and_coadd

warnings.filterwarnings('ignore', category=AstropyWarning, append=True)


now_time = datetime.now()
default_day = now_time.strftime('%Y%m%d')

parser = argparse.ArgumentParser(description='sub_dir')

parser.add_argument('--sub_dir', type=str, default=default_day, help='sub dir like 20250335')


args = parser.parse_args()

logger = logging.getLogger()
logger.setLevel(logging.DEBUG)
# logger.setLevel(logging.INFO)
logger.setLevel(logging.WARNING)

# 控制台Handler（INFO级别）
console_handler = logging.StreamHandler()
# console_handler.setLevel(logging.INFO)
console_handler.setLevel(logging.DEBUG)

os.makedirs('log_auto_delete', exist_ok=True)
# 文件Handler（DEBUG级别）
file_handler = RotatingFileHandler(
    f'log_auto_delete/log_auto_delete_{datetime.now().strftime("%Y%m%d_%H%M")}.log',
    maxBytes=10*1024*1024,
    backupCount=30,
    encoding='utf-8'
)

formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

logger.addHandler(console_handler)
logger.addHandler(file_handler)

files_for_proc = []

# 获取上个月的日期
last_month = now_time - relativedelta(months=1)
last_month_str = last_month.strftime('%Y%m')



log_make_temp_path = Path('~/log_make_temp').expanduser()
log_make_temp_pattern = os.path.expanduser(f'**/make_temp_{last_month_str}*.log')
log_make_temp_pattern_multi = os.path.expanduser(f'**/multi_make_temp_{last_month_str}*.log')

log_make_calibration_path = Path('~/log_make_calibration/make_calibration_').expanduser()
log_make_calibration_pattern = os.path.expanduser(f'**/make_calibration_{last_month_str}*.log')
log_make_calibration_pattern_multi = os.path.expanduser(f'**/multi_make_calibration_{last_month_str}*.log')

log_core_pool_path = Path('~/log_core_pool/').expanduser()
log_core_pool_pattern = os.path.expanduser(f'**/auto*{last_month_str}*.log')

img_core_pool = Path('~/img_core_pool/').expanduser()
img_core_pool_pattern = os.path.expanduser(f'**/task_timeline_{last_month_str}*.png')


XMatch_file_path = Path('/root/.astropy/cache')
xmatch_file_paattern = f"**/astroquery/XMatch/*.pickle"

log_temp_list = list(log_make_temp_path.glob(log_make_temp_pattern))
log_temp_list_multi = list(log_make_temp_path.glob(log_make_temp_pattern_multi))
logger.warning(f"find {len(log_temp_list)} log_temp in {log_make_temp_path}")




log_calibration_list = list(log_make_calibration_path.glob(log_make_calibration_pattern))
log_calibration_list_multi = list(log_make_calibration_path.glob(log_make_calibration_pattern_multi))
logger.warning(f"find {len(log_calibration_list)} log_calibration in {log_make_calibration_path}")

log_core_list = list(log_core_pool_path.glob(log_core_pool_pattern))
logger.warning(f"find {len(log_core_list)} log_core in {log_core_pool_path}")

img_core_list = list(img_core_pool.glob(img_core_pool_pattern))
logger.warning(f"find {len(img_core_list)} img_core in {img_core_pool}")

xmatch_file_list = list(XMatch_file_path.glob(xmatch_file_paattern))
logger.warning(f"find {len(xmatch_file_list)} xmatch_file in {XMatch_file_path}")

# 增加删除文件
for file_path in log_temp_list + log_temp_list_multi + log_calibration_list + log_calibration_list_multi + log_core_list + img_core_list + xmatch_file_list:
    os.remove(file_path)
    logger.warning(f"delete {file_path}")






